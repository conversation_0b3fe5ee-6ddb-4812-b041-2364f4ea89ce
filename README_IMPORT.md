# Database Import Script

This script reads JSON files from the `db_exports` folder and imports the data back into PostgreSQL tables on a remote server.

## Prerequisites

1. **SSH Tunnel**: Ensure you have an active SSH tunnel to the remote PostgreSQL server:
   ```bash
   ssh -L 5433:localhost:5432 root@*************
   ```
   Keep this terminal open while running the import script.

2. **Dependencies**: Make sure you have the required Node.js packages installed:
   ```bash
   npm install
   ```

3. **JSON Files**: Ensure the `db_exports` folder contains the JSON files you want to import.

## Usage

### Import All Tables

To import all JSON files from the `db_exports` folder:

```bash
node import_json_to_db.js
```

### Import All Tables (Clear Existing Data)

⚠️ **WARNING**: This will delete all existing data from the tables before importing!

```bash
node import_json_to_db.js --clear
```

### Import Specific Table

To import data for a specific table only:

```bash
node import_json_to_db.js table_name
```

For example, to import only the `user` table:
```bash
node import_json_to_db.js user
```

### Import Specific Table (Clear Existing Data)

To clear existing data and import a specific table:

```bash
node import_json_to_db.js table_name --clear
```

## Features

### 🚀 **Batch Processing**
- Inserts data in batches of 100 records for optimal performance
- Configurable batch size (modify `BATCH_SIZE` constant in the script)

### 🛡️ **Error Handling**
- Comprehensive error handling with detailed error messages
- Continues processing other tables if one fails
- Logs progress and batch completion status

### 🔄 **Data Type Handling**
- Automatically handles different data types:
  - Strings and numbers
  - Booleans
  - NULL values
  - JSON objects and arrays
  - Date strings

### 📊 **Progress Tracking**
- Shows progress for each table being imported
- Displays batch completion status
- Provides summary of total records imported

### 🔒 **Safe Operations**
- Column names are properly escaped to prevent SQL injection
- Parameterized queries for safe data insertion
- Optional data clearing (requires explicit flag)

## Configuration

You can modify the following settings in the script:

```javascript
// Batch size for inserts (default: 100)
const BATCH_SIZE = 100;

// Database connection settings
const client = new Client({
  host: 'localhost',
  port: 5433,
  user: 'postgres',
  password: 'SuperPassword123!',
  database: 'careerireland',
  ssl: { rejectUnauthorized: false }
});
```

## File Structure

The script expects JSON files in the following format:

```
db_exports/
├── table1.json
├── table2.json
└── table3.json
```

Each JSON file should contain an array of objects:

```json
[
  {
    "id": "123",
    "name": "Example",
    "created_at": "2025-01-01T00:00:00.000Z",
    "metadata": {"key": "value"}
  },
  {
    "id": "456",
    "name": "Another Example",
    "created_at": "2025-01-02T00:00:00.000Z",
    "metadata": {"key": "value2"}
  }
]
```

## Troubleshooting

### Connection Issues
- Ensure the SSH tunnel is active and running
- Verify the database credentials in the script
- Check that the remote server is accessible

### Data Import Issues
- Verify JSON file format (must be valid JSON array)
- Check for data type mismatches
- Ensure table exists in the target database
- Review column names match between JSON and database schema

### Performance Issues
- Reduce `BATCH_SIZE` if experiencing memory issues
- Increase `BATCH_SIZE` for better performance with large datasets
- Consider importing tables individually for very large datasets

## Example Output

```
✅ Connected to remote PostgreSQL server
📋 Found 30 JSON files to import: [ 'admin', 'agent', 'application', ... ]

🔄 Processing table: admin
📖 Reading data from /path/to/db_exports/admin.json...
📋 Columns for admin: [ 'id', 'name', 'email', 'created_at' ]
📥 Inserting 150 records into admin...
  ✅ Inserted batch: 100/150 records
  ✅ Inserted batch: 150/150 records
✅ Successfully inserted 150 records into admin

🔄 Processing table: agent
...

🎉 All tables imported successfully!
🔌 Connection closed
```

## Safety Notes

- Always backup your database before running import operations
- Test with a small dataset first
- Use the `--clear` flag with extreme caution
- Verify data integrity after import completion
- Consider running imports during maintenance windows for production systems
