[{"id": "af_1750247288042_6ca6xmoqs", "application_id": "cmc1w1mnb0003jkstpg80zz0w", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-18T06:18:08.043Z", "updated_at": "2025-06-18T06:18:08.043Z"}, {"id": "af_1750247288042_0pywsfr9d", "application_id": "cmc1w1mnb0003jkstpg80zz0w", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-18T06:18:08.043Z", "updated_at": "2025-06-18T06:18:08.043Z"}, {"id": "af_1750247288042_1vfbwr7mk", "application_id": "cmc1w1mnb0003jkstpg80zz0w", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Male", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-18T06:18:08.043Z", "updated_at": "2025-06-18T06:35:31.702Z"}, {"id": "af_1750247288042_3v0v8nb29", "application_id": "cmc1w1mnb0003jkstpg80zz0w", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "", "field_options": [], "show_to_client": true, "created_at": "2025-06-18T06:18:08.043Z", "updated_at": "2025-06-18T06:35:31.704Z"}, {"id": "af_1750328249630_ft4gievm7", "application_id": "cmc388x0a0004jkp6ej3qf0la", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-19T04:47:29.631Z", "updated_at": "2025-06-19T04:47:29.631Z"}, {"id": "af_1750328249630_63eqe9bh9", "application_id": "cmc388x0a0004jkp6ej3qf0la", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-19T04:47:29.631Z", "updated_at": "2025-06-19T04:47:29.631Z"}, {"id": "af_1750418728651_9nfxuo5yq", "application_id": "cmc4q472g0016jk4qm6lzq0sh", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T05:55:28.652Z", "updated_at": "2025-06-20T05:55:28.652Z"}, {"id": "af_1750418728651_skh21jrkt", "application_id": "cmc4q472g0016jk4qm6lzq0sh", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-20T05:55:28.652Z", "updated_at": "2025-06-20T05:55:28.652Z"}, {"id": "af_1750418728651_v7vp9sdhx", "application_id": "cmc4q472g0016jk4qm6lzq0sh", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-20T05:55:28.652Z", "updated_at": "2025-06-20T05:55:28.652Z"}, {"id": "af_1750418728651_ozjpf7msl", "application_id": "cmc4q472g0016jk4qm6lzq0sh", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-20T05:55:28.652Z", "updated_at": "2025-06-20T05:55:28.652Z"}, {"id": "af_1750328249630_8te1e6jqc", "application_id": "cmc388x0a0004jkp6ej3qf0la", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Male", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-19T04:47:29.631Z", "updated_at": "2025-06-19T06:09:10.072Z"}, {"id": "af_1750328249630_9v8q8wgfy", "application_id": "cmc388x0a0004jkp6ej3qf0la", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-06-19T04:47:29.631Z", "updated_at": "2025-06-19T06:09:10.074Z"}, {"id": "af_1750335174664_w6rr8cly5", "application_id": "cmc3cdced000rjkhnwrfkkyz7", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-19T06:42:54.665Z", "updated_at": "2025-06-19T06:42:54.665Z"}, {"id": "af_1750335174664_un1q01hxe", "application_id": "cmc3cdced000rjkhnwrfkkyz7", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-19T06:42:54.665Z", "updated_at": "2025-06-19T06:42:54.665Z"}, {"id": "af_1750335174664_hcslj907c", "application_id": "cmc3cdced000rjkhnwrfkkyz7", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-19T06:42:54.665Z", "updated_at": "2025-06-19T06:42:54.665Z"}, {"id": "af_1750335174664_xw90xuq2x", "application_id": "cmc3cdced000rjkhnwrfkkyz7", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-19T06:42:54.665Z", "updated_at": "2025-06-19T06:42:54.665Z"}, {"id": "af_1750441165267_wylo02y3r", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750441165267_4563mm09u", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750441165267_enwwlagth", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750441165267_jnnbl67iu", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750441165267_eu43u5hc1", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750441165267_hztvfbw7m", "application_id": "cmc53h39s001sjk4qrnhgdd2d", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-20T12:09:25.268Z", "updated_at": "2025-06-20T12:09:25.268Z"}, {"id": "af_1750753525553_edo2se5rg", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-24T02:55:25.554Z"}, {"id": "af_1750494682975_kyb59cd6d", "application_id": "cmc5zc5ss0003jkd9cmo3nst1", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Female", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-21T03:01:22.976Z", "updated_at": "2025-06-23T07:08:37.941Z"}, {"id": "af_1750494682975_urm90lpoj", "application_id": "cmc5zc5ss0003jkd9cmo3nst1", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-06-21T03:01:22.976Z", "updated_at": "2025-06-23T07:08:37.943Z"}, {"id": "af_1750753525553_mijeurch5", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": "2025-06-24", "field_options": [], "show_to_client": true, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-25T02:12:09.253Z"}, {"id": "af_1750335064105_7eiwhmltm", "application_id": "cmc3caz390007jkhnbn5d8ium", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Male", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-19T06:41:04.105Z", "updated_at": "2025-06-20T04:29:24.129Z"}, {"id": "af_1750335064105_8z300wx7c", "application_id": "cmc3caz390007jkhnbn5d8ium", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-06-19T06:41:04.105Z", "updated_at": "2025-06-20T04:29:24.143Z"}, {"id": "af_1750335064105_7dw846x41", "application_id": "cmc3caz390007jkhnbn5d8ium", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": "", "field_options": [], "show_to_client": false, "created_at": "2025-06-19T06:41:04.105Z", "updated_at": "2025-06-20T05:01:06.146Z"}, {"id": "af_1750335064105_i0wb0f9cl", "application_id": "cmc3caz390007jkhnbn5d8ium", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": "", "field_options": [], "show_to_client": false, "created_at": "2025-06-19T06:41:04.105Z", "updated_at": "2025-06-20T05:01:06.147Z"}, {"id": "af_1750417923824_ulvtzj8pt", "application_id": "cmc4pmy24000ijk4qo91xg729", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-06-20T05:42:03.824Z", "updated_at": "2025-06-20T05:42:03.824Z"}, {"id": "af_1750417923824_ksrw4qoeq", "application_id": "cmc4pmy24000ijk4qo91xg729", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-20T05:42:03.824Z", "updated_at": "2025-06-20T05:42:03.824Z"}, {"id": "af_1750417923824_qktkltpgi", "application_id": "cmc4pmy24000ijk4qo91xg729", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-20T05:42:03.824Z", "updated_at": "2025-06-20T05:42:03.824Z"}, {"id": "af_1750417923824_hdta1aggq", "application_id": "cmc4pmy24000ijk4qo91xg729", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-06-20T05:42:03.824Z", "updated_at": "2025-06-20T05:42:03.824Z"}, {"id": "af_1750753525553_iyp56ypyk", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": "<EMAIL>", "field_options": [], "show_to_client": true, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-25T02:12:09.254Z"}, {"id": "af_1750494450572_9trk8chwn", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Male", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:33.435Z"}, {"id": "af_1751474362644_4a8hokute", "application_id": "cmcm6m3gs000djkj5friwn85j", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": "4 and 3", "field_options": [], "show_to_client": true, "created_at": "2025-07-02T11:09:22.645Z", "updated_at": "2025-07-02T11:20:38.027Z"}, {"id": "af_1750494682975_r3l6x5x9d", "application_id": "cmc5zc5ss0003jkd9cmo3nst1", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": "kkkkkkkkkkk", "field_options": [], "show_to_client": false, "created_at": "2025-06-21T03:01:22.976Z", "updated_at": "2025-06-23T07:39:13.134Z"}, {"id": "af_1750494450572_c39y1u3to", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": "2025-06-15", "field_options": [], "show_to_client": true, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:33.436Z"}, {"id": "af_1750494682975_7g16abq6b", "application_id": "cmc5zc5ss0003jkd9cmo3nst1", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": "", "field_options": [], "show_to_client": false, "created_at": "2025-06-21T03:01:22.976Z", "updated_at": "2025-06-23T07:39:13.135Z"}, {"id": "af_1751474362644_bg3gjubkg", "application_id": "cmcm6m3gs000djkj5friwn85j", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": "", "field_options": [], "show_to_client": true, "created_at": "2025-07-02T11:09:22.645Z", "updated_at": "2025-07-02T11:20:38.041Z"}, {"id": "af_1750753525553_lcichnx27", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": "Bhanu Nagar", "field_options": [], "show_to_client": true, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-25T02:12:09.251Z"}, {"id": "af_1750753525553_jr8tluf3y", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-25T02:12:09.255Z"}, {"id": "af_1750753525553_gyrqpn6og", "application_id": "cmca9g20a0005jkh8d3346fnp", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Male", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-06-24T02:55:25.554Z", "updated_at": "2025-06-25T02:12:09.256Z"}, {"id": "af_1751474362644_23e1g8tep", "application_id": "cmcm6m3gs000djkj5friwn85j", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": "", "field_options": [], "show_to_client": true, "created_at": "2025-07-02T11:09:22.645Z", "updated_at": "2025-07-02T11:20:38.042Z"}, {"id": "af_1752143349084_nbcmcxzvb", "application_id": "cmcx8wspg0003jk73i28tkcyt", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T04:59:09.085Z", "updated_at": "2025-07-10T04:59:09.085Z"}, {"id": "af_1750494450572_in0fmijgq", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": "", "field_options": [], "show_to_client": false, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:07.048Z"}, {"id": "af_1750494450572_wx63grybi", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:33.433Z"}, {"id": "af_1750494450572_cjz44evvm", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": "<EMAIL>", "field_options": [], "show_to_client": true, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:33.437Z"}, {"id": "af_1750494450572_i983e7jcu", "application_id": "cmc5z76h4002ijk4qvihijsfp", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": "Bhanu Nagar", "field_options": [], "show_to_client": true, "created_at": "2025-06-21T02:57:30.573Z", "updated_at": "2025-06-25T09:58:33.438Z"}, {"id": "af_1752143349084_v41pdz7bw", "application_id": "cmcx8wspg0003jk73i28tkcyt", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T04:59:09.085Z", "updated_at": "2025-07-10T04:59:09.085Z"}, {"id": "af_1752143349084_w7z8hwecl", "application_id": "cmcx8wspg0003jk73i28tkcyt", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T04:59:09.085Z", "updated_at": "2025-07-10T04:59:09.085Z"}, {"id": "af_1752144355173_4uunancno", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752144355173_b0mwmdgk0", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752144355173_li8kbdz4f", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752144355173_vbks7un9o", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752144355173_0f59e5k7j", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752144355173_g2oqk8la5", "application_id": "cmcx9id0i0005jkt0kcnugqzm", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-10T05:15:55.174Z", "updated_at": "2025-07-10T05:15:55.174Z"}, {"id": "af_1752148670496_90qj9cc51", "application_id": "cmcxc2uqk0004jk0kn19ndiwi", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-10T06:27:50.497Z", "updated_at": "2025-07-10T06:27:50.497Z"}, {"id": "af_1752148670496_d9g1eltqw", "application_id": "cmcxc2uqk0004jk0kn19ndiwi", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-07-10T06:27:50.497Z", "updated_at": "2025-07-10T06:27:50.497Z"}, {"id": "af_1752148670496_u734drlne", "application_id": "cmcxc2uqk0004jk0kn19ndiwi", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-10T06:27:50.497Z", "updated_at": "2025-07-10T06:27:50.497Z"}, {"id": "af_1752148670496_ydergdeeo", "application_id": "cmcxc2uqk0004jk0kn19ndiwi", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-10T06:27:50.497Z", "updated_at": "2025-07-10T06:27:50.497Z"}, {"id": "af_1752234938232_sgexbsp2a", "application_id": "cmcyrfvcc0003jkd75rbzi6l0", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-11T06:25:38.233Z", "updated_at": "2025-07-11T06:25:38.233Z"}, {"id": "af_1752234938232_ove7rgqf9", "application_id": "cmcyrfvcc0003jkd75rbzi6l0", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-11T06:25:38.233Z", "updated_at": "2025-07-11T06:25:38.233Z"}, {"id": "af_1752234938232_h1w5jq8v2", "application_id": "cmcyrfvcc0003jkd75rbzi6l0", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-11T06:25:38.233Z", "updated_at": "2025-07-11T06:25:38.233Z"}, {"id": "af_1752299662434_i1oue120a", "application_id": "cmcztz4v10007jkwu8zzhyaej", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-12T00:24:22.435Z", "updated_at": "2025-07-12T00:24:22.435Z"}, {"id": "af_1752299662434_elqt3azu4", "application_id": "cmcztz4v10007jkwu8zzhyaej", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-07-12T00:24:22.435Z", "updated_at": "2025-07-12T00:24:22.435Z"}, {"id": "af_1752299662434_6i6uha7r2", "application_id": "cmcztz4v10007jkwu8zzhyaej", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-12T00:24:22.435Z", "updated_at": "2025-07-12T00:24:22.435Z"}, {"id": "af_1752299662434_m87v2j7f4", "application_id": "cmcztz4v10007jkwu8zzhyaej", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-12T00:24:22.435Z", "updated_at": "2025-07-12T00:24:22.435Z"}, {"id": "af_1752306811250_90vge3jh7", "application_id": "cmczy8cx90003jkivalzzeotv", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-12T02:23:31.251Z", "updated_at": "2025-07-12T02:23:31.251Z"}, {"id": "af_1752306811250_fq101en8j", "application_id": "cmczy8cx90003jkivalzzeotv", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-07-12T02:23:31.251Z", "updated_at": "2025-07-12T02:23:31.251Z"}, {"id": "af_1752306811250_kkpw2tpp6", "application_id": "cmczy8cx90003jkivalzzeotv", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-12T02:23:31.251Z", "updated_at": "2025-07-12T02:23:31.251Z"}, {"id": "af_1752306811250_n3xbpwksq", "application_id": "cmczy8cx90003jkivalzzeotv", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-07-12T02:23:31.251Z", "updated_at": "2025-07-12T02:23:31.251Z"}, {"id": "af_1752312828207_d7aepr49g", "application_id": "cmd01tbn00004jk5y8wixpgcd", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-12T04:03:48.208Z", "updated_at": "2025-07-12T04:03:48.208Z"}, {"id": "af_1752312828208_j24tuigm7", "application_id": "cmd01tbn00004jk5y8wixpgcd", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-12T04:03:48.208Z", "updated_at": "2025-07-12T04:03:48.208Z"}, {"id": "af_1752312828208_vglgcogue", "application_id": "cmd01tbn00004jk5y8wixpgcd", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-12T04:03:48.208Z", "updated_at": "2025-07-12T04:03:48.208Z"}, {"id": "af_1752497749012_5wxdq7dm3", "application_id": "cmd33wtfk000fjkrtwnw8gj6p", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-14T07:25:49.013Z", "updated_at": "2025-07-14T07:25:49.013Z"}, {"id": "af_1752497749012_gea2zmybi", "application_id": "cmd33wtfk000fjkrtwnw8gj6p", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-14T07:25:49.013Z", "updated_at": "2025-07-14T07:25:49.013Z"}, {"id": "af_1752497749012_g15mvppgf", "application_id": "cmd33wtfk000fjkrtwnw8gj6p", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-14T07:25:49.013Z", "updated_at": "2025-07-14T07:25:49.013Z"}, {"id": "af_1753095237611_80e9xjlbj", "application_id": "cmdczn2ld0013jkrtvtb24mi0", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-21T05:23:57.613Z", "updated_at": "2025-07-21T05:23:57.613Z"}, {"id": "af_1753095237612_3jgfr38qn", "application_id": "cmdczn2ld0013jkrtvtb24mi0", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-21T05:23:57.613Z", "updated_at": "2025-07-21T05:23:57.613Z"}, {"id": "af_1753095237612_2yaflwfza", "application_id": "cmdczn2ld0013jkrtvtb24mi0", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-21T05:23:57.613Z", "updated_at": "2025-07-21T05:23:57.613Z"}, {"id": "af_1753881007331_55bhrl1sr", "application_id": "cmdpzgudo0004jksmf681xy04", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-30T07:40:07.333Z", "updated_at": "2025-07-30T07:40:07.333Z"}, {"id": "af_1753881007331_aqo70wlby", "application_id": "cmdpzgudo0004jksmf681xy04", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-30T07:40:07.333Z", "updated_at": "2025-07-30T07:40:07.333Z"}, {"id": "af_1753881007331_px87w0lfo", "application_id": "cmdpzgudo0004jksmf681xy04", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-07-30T07:40:07.333Z", "updated_at": "2025-07-30T07:40:07.333Z"}, {"id": "af_1754361694078_0uz7ep853", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T21:11:34.080Z"}, {"id": "af_1754578052333_qnymsrs4c", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}, {"id": "af_1754583405958_xk81puapg", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754583405958_1v7j7hmkz", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754583405958_c7am2kb82", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754418385484_1oirgt9o9", "application_id": "cmdyveq0f001ljksmw1y5d7e2", "stage_order": 1, "field_name": "Company Address", "field_type": "text", "required": true, "field_value": "mmmmmm77", "field_options": [], "show_to_client": true, "created_at": "2025-08-05T12:56:25.493Z", "updated_at": "2025-08-06T16:21:47.089Z"}, {"id": "af_1754361694078_ah1vb2x1v", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": "<PERSON><PERSON><PERSON>", "field_options": [], "show_to_client": true, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T23:14:51.281Z"}, {"id": "af_1754361694078_z3il13t9b", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": "Female", "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T23:14:51.293Z"}, {"id": "af_1754361694078_5l6kznske", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": "1990-01-01", "field_options": [], "show_to_client": true, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T23:14:51.296Z"}, {"id": "af_1754361694078_9o3tcv8d5", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": "<EMAIL>", "field_options": [], "show_to_client": true, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T23:14:51.297Z"}, {"id": "af_1754361694078_5f1pym7se", "application_id": "cmdxxnmnm000ljksmvjentvro", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": "123235456y6uyjngn, Dublin", "field_options": [], "show_to_client": true, "created_at": "2025-08-04T21:11:34.080Z", "updated_at": "2025-08-04T23:14:51.298Z"}, {"id": "af_1754418385484_7ytuj6u2z", "application_id": "cmdyveq0f001ljksmw1y5d7e2", "stage_order": 1, "field_name": "EU and Non EU Employees", "field_type": "text", "required": true, "field_value": "kkkkkk", "field_options": [], "show_to_client": true, "created_at": "2025-08-05T12:56:25.493Z", "updated_at": "2025-08-06T16:21:47.091Z"}, {"id": "af_1754418385484_74sk4vqjw", "application_id": "cmdyveq0f001ljksmw1y5d7e2", "stage_order": 1, "field_name": "Company Name", "field_type": "text", "required": true, "field_value": "", "field_options": [], "show_to_client": true, "created_at": "2025-08-05T12:56:25.493Z", "updated_at": "2025-08-06T16:21:47.092Z"}, {"id": "af_1754583405958_3h72geevc", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754583405958_areco9g3a", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754583405958_be2m9ss0d", "application_id": "cme1lnon50003jko2xu4assn6", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-08-07T10:46:45.959Z", "updated_at": "2025-08-07T10:46:45.959Z"}, {"id": "af_1754543748786_aw8fha81p", "application_id": "cme0y1oyv0013jk005k2t3g2v", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-06T23:45:48.788Z", "updated_at": "2025-08-06T23:45:48.788Z"}, {"id": "af_1754543748786_f9jjgbge6", "application_id": "cme0y1oyv0013jk005k2t3g2v", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-08-06T23:45:48.788Z", "updated_at": "2025-08-06T23:45:48.788Z"}, {"id": "af_1754543748786_lrwiedxh6", "application_id": "cme0y1oyv0013jk005k2t3g2v", "stage_order": 3, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-08-06T23:45:48.788Z", "updated_at": "2025-08-06T23:45:48.788Z"}, {"id": "af_1754543748786_5f3lqvizw", "application_id": "cme0y1oyv0013jk005k2t3g2v", "stage_order": 4, "field_name": "Note", "field_type": "text", "required": false, "field_value": null, "field_options": [], "show_to_client": false, "created_at": "2025-08-06T23:45:48.788Z", "updated_at": "2025-08-06T23:45:48.788Z"}, {"id": "af_1754578052333_m0m9dz9b7", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 1, "field_name": "Name", "field_type": "text", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}, {"id": "af_1754578052333_x9z046bo8", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 1, "field_name": "Gender", "field_type": "select", "required": true, "field_value": null, "field_options": ["Male", "Female"], "show_to_client": true, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}, {"id": "af_1754578052333_z9b5w7qai", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 1, "field_name": "Date of Birth", "field_type": "date", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}, {"id": "af_1754578052333_c57dbowhd", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 1, "field_name": "Email", "field_type": "email", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}, {"id": "af_1754578052333_edz2an6gn", "application_id": "cme1igxr70023jk006en1vyvv", "stage_order": 1, "field_name": "Address", "field_type": "textarea", "required": true, "field_value": null, "field_options": [], "show_to_client": true, "created_at": "2025-08-07T09:17:32.335Z", "updated_at": "2025-08-07T09:17:32.335Z"}]