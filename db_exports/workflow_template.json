[{"id": "cmc1u4hjn000ajkf9jfhxd86i", "name": "Work Permit Applications Template", "description": "", "serviceType": "immigration", "serviceId": "cmc1tt5wm0001jkf96f6by8na", "isActive": true, "workflowTemplate": [{"documents": [], "stageName": "Personal Details", "customForm": [{"options": [], "required": true, "fieldName": "Name", "fieldType": "text", "showToClient": true}, {"options": ["Male", "Female"], "required": true, "fieldName": "Gender", "fieldType": "select", "showToClient": true}], "stageOrder": 1, "showToClient": true, "documentsRequired": false, "customFormRequired": true}, {"documents": [{"required": true, "documentName": "Updated CV"}, {"required": true, "documentName": "Contract  (signed by employer & Employee) "}, {"required": true, "documentName": "Birth Certificate"}, {"required": false, "documentName": "Passport"}], "stageName": "Upload Document", "customForm": [], "stageOrder": 2, "showToClient": true, "documentsRequired": true, "customFormRequired": false}, {"documents": [], "stageName": "Application Filing", "customForm": [{"options": [], "required": false, "fieldName": "Note", "fieldType": "text", "showToClient": false}], "stageOrder": 3, "showToClient": true, "documentsRequired": false, "customFormRequired": true}, {"documents": [], "stageName": "Application Review", "customForm": [{"options": [], "required": false, "fieldName": "Note", "fieldType": "text", "showToClient": false}], "stageOrder": 4, "showToClient": true, "documentsRequired": false, "customFormRequired": true}, {"documents": [], "stageName": "Completed", "customForm": [], "stageOrder": 5, "showToClient": true, "documentsRequired": false, "customFormRequired": false}], "createdBy": "<PERSON><PERSON><PERSON>", "updatedBy": "<PERSON><PERSON><PERSON>", "createdAt": "2025-06-18T05:24:22.163Z", "updatedAt": "2025-08-06T23:54:26.409Z", "isDefault": false}, {"id": "cmcm6g7540009jkj5ssbs046l", "name": "CSEP Application", "description": "Template for CSEP application", "serviceType": "immigration", "serviceId": "cmcm67kc00008jkj5lzizg1fe", "isActive": true, "workflowTemplate": [{"documents": [{"required": true, "documentName": "Passport"}, {"required": true, "documentName": "Contract  (signed by employer & Employee) "}, {"required": false, "documentName": "Updated CV"}], "stageName": "Upload Document", "customForm": [{"options": [], "required": true, "fieldName": "EU and Non EU Employees", "fieldType": "text", "showToClient": true}, {"options": [], "required": true, "fieldName": "Company Name", "fieldType": "text", "showToClient": true}, {"options": [], "required": true, "fieldName": "Company Address", "fieldType": "text", "showToClient": true}], "stageOrder": 1, "showToClient": true, "documentsRequired": true, "customFormRequired": true}, {"documents": [], "stageName": "Application Filing", "customForm": [], "stageOrder": 2, "showToClient": true, "documentsRequired": false, "customFormRequired": false}, {"documents": [], "stageName": "Application Review", "customForm": [], "stageOrder": 3, "showToClient": true, "documentsRequired": false, "customFormRequired": false}, {"documents": [], "stageName": "Completed", "customForm": [], "stageOrder": 4, "showToClient": true, "documentsRequired": false, "customFormRequired": false}], "createdBy": "<PERSON><PERSON><PERSON>", "updatedBy": "<PERSON><PERSON><PERSON>", "createdAt": "2025-07-02T11:04:47.464Z", "updatedAt": "2025-07-10T05:05:21.880Z", "isDefault": false}, {"id": "cmc53etza001ojk4qoos9i9e7", "name": "Naturalization Application Template", "description": "", "serviceType": "immigration", "serviceId": "cmc3h26830000jk8bq358414x", "isActive": true, "workflowTemplate": [{"documents": [], "stageName": "Personal Details", "customForm": [{"options": [], "required": true, "fieldName": "Name", "fieldType": "text", "showToClient": true}, {"options": ["Male", "Female"], "required": true, "fieldName": "Gender", "fieldType": "select", "showToClient": true}, {"options": [], "required": true, "fieldName": "Date of Birth", "fieldType": "date", "showToClient": true}, {"options": [], "required": true, "fieldName": "Email", "fieldType": "email", "showToClient": true}, {"options": [], "required": true, "fieldName": "Address", "fieldType": "textarea", "showToClient": true}, {"options": [], "required": false, "fieldName": "Address", "fieldType": "textarea", "showToClient": true}], "stageOrder": 1, "showToClient": true, "documentsRequired": false, "customFormRequired": true}, {"documents": [{"required": true, "documentName": "Passport"}, {"required": true, "documentName": "Birth Certificate"}, {"required": true, "documentName": "Contract  (signed by employer & Employee) "}, {"required": false, "documentName": "Updated CV"}], "stageName": "Upload Document", "customForm": [], "stageOrder": 2, "showToClient": true, "documentsRequired": true, "customFormRequired": false}, {"documents": [], "stageName": "Application Filing", "customForm": [{"options": [], "required": false, "fieldName": "Note", "fieldType": "text", "showToClient": false}], "stageOrder": 3, "showToClient": true, "documentsRequired": false, "customFormRequired": true}, {"documents": [], "stageName": "Application Review", "customForm": [], "stageOrder": 4, "showToClient": true, "documentsRequired": false, "customFormRequired": false}], "createdBy": "<PERSON><PERSON><PERSON>", "updatedBy": "<PERSON><PERSON><PERSON>", "createdAt": "2025-06-20T12:07:39.899Z", "updatedAt": "2025-07-10T05:05:23.464Z", "isDefault": true}, {"id": "cme14m1wy001kjk00z5xikp8t", "name": "GWP", "description": "", "serviceType": "immigration", "serviceId": "cmc1tt5wm0001jkf96f6by8na", "isActive": true, "workflowTemplate": [{"documents": [{"required": false, "documentName": "Contract  (signed by employer & Employee) "}, {"required": false, "documentName": "Passport"}], "stageName": "Upload Document", "customForm": [], "stageOrder": 1, "showToClient": true, "documentsRequired": true, "customFormRequired": false}], "createdBy": "<PERSON><PERSON><PERSON>", "updatedBy": "<PERSON><PERSON><PERSON>", "createdAt": "2025-08-07T02:49:36.369Z", "updatedAt": "2025-08-07T03:00:44.522Z", "isDefault": false}]