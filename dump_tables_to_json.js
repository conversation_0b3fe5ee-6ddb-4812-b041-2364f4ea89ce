// dump_tables_to_json.js
import { Client } from 'pg';
import fs from 'fs';
import path from 'path';

// *************
// ssh -L 5433:localhost:5432 root@ipaddress
// 5433 → local port you’ll connect to
// localhost:5432 → where PostgreSQL is running on the remote server
// root@ipaddress → your SSH credentials
// Leave this terminal open — it keeps the tunnel alive.

// Database connection config
const client = new Client({
  host: 'localhost',     // e.g., db.abcd.supabase.co
  port: 5433,
  user: 'postgres',         // e.g., postgres
  password: 'SuperPassword123!',
  database: 'careerireland',     // e.g., postgres
  ssl: { rejectUnauthorized: false } // required for Supabase & some remote servers
});

async function dumpTables() {
  try {
    await client.connect();
    console.log('✅ Connected to remote PostgreSQL server');

    // 1. Get all table names from public schema
    const tableRes = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    const tables = tableRes.rows.map(row => row.table_name);
    console.log(`📋 Found ${tables.length} tables:`, tables);

    // Ensure output directory exists
    const outputDir = path.join(process.cwd(), 'db_exports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }

    // 2. Loop through each table and export data
    for (const table of tables) {
      console.log(`📦 Exporting table: ${table}...`);
      const dataRes = await client.query(`SELECT * FROM ${table}`);
      const jsonPath = path.join(outputDir, `${table}.json`);
      fs.writeFileSync(jsonPath, JSON.stringify(dataRes.rows, null, 2), 'utf-8');
      console.log(`✅ Saved ${table} → ${jsonPath}`);
    }

    console.log('🎉 All tables exported successfully!');
  } catch (err) {
    console.error('❌ Error exporting tables:', err);
  } finally {
    await client.end();
    console.log('🔌 Connection closed');
  }
}

dumpTables();
