// import_json_to_db.js
import { Client } from 'pg';
import fs from 'fs';
import path from 'path';

// Database connection config (same as export script)
const client = new Client({
  host: 'localhost',     // Connect through SSH tunnel
  port: 5433,
  user: 'postgres',
  password: 'SuperPassword123!',
  database: 'careerireland',
  ssl: { rejectUnauthorized: false }
});

// Configuration
const BATCH_SIZE = 100; // Number of records to insert in each batch
const EXPORTS_DIR = path.join(process.cwd(), 'db_exports');

/**
 * Escape column names for PostgreSQL queries
 */
function escapeColumnName(columnName) {
  return `"${columnName}"`;
}

/**
 * Convert JavaScript values to PostgreSQL-compatible format
 */
function formatValue(value) {
  if (value === null || value === undefined) {
    return null;
  }
  
  if (typeof value === 'object') {
    // Handle arrays and objects as JSON
    return JSON.stringify(value);
  }
  
  if (typeof value === 'boolean') {
    return value;
  }
  
  if (typeof value === 'number') {
    return value;
  }
  
  // Handle dates and strings
  return value;
}

/**
 * Generate INSERT query with proper parameter placeholders
 */
function generateInsertQuery(tableName, columns, batchSize) {
  const escapedColumns = columns.map(escapeColumnName).join(', ');
  const placeholders = [];
  
  for (let i = 0; i < batchSize; i++) {
    const rowPlaceholders = columns.map((_, colIndex) => 
      `$${i * columns.length + colIndex + 1}`
    ).join(', ');
    placeholders.push(`(${rowPlaceholders})`);
  }
  
  return `INSERT INTO ${escapeColumnName(tableName)} (${escapedColumns}) VALUES ${placeholders.join(', ')}`;
}

/**
 * Insert data in batches for better performance
 */
async function insertDataInBatches(tableName, data, columns) {
  if (data.length === 0) {
    console.log(`⚠️  No data to insert for table: ${tableName}`);
    return;
  }

  console.log(`📥 Inserting ${data.length} records into ${tableName}...`);
  
  let insertedCount = 0;
  
  for (let i = 0; i < data.length; i += BATCH_SIZE) {
    const batch = data.slice(i, i + BATCH_SIZE);
    const query = generateInsertQuery(tableName, columns, batch.length);
    
    // Flatten batch data into parameter array
    const params = [];
    for (const row of batch) {
      for (const column of columns) {
        params.push(formatValue(row[column]));
      }
    }
    
    try {
      await client.query(query, params);
      insertedCount += batch.length;
      console.log(`  ✅ Inserted batch: ${insertedCount}/${data.length} records`);
    } catch (error) {
      console.error(`  ❌ Error inserting batch for ${tableName}:`, error.message);
      console.error(`  Query: ${query}`);
      console.error(`  Sample row:`, batch[0]);
      throw error;
    }
  }
  
  console.log(`✅ Successfully inserted ${insertedCount} records into ${tableName}`);
}

/**
 * Clear existing data from table (optional - be careful!)
 */
async function clearTable(tableName, confirm = false) {
  if (!confirm) {
    console.log(`⚠️  Skipping table clear for ${tableName} (set confirm=true to enable)`);
    return;
  }
  
  try {
    await client.query(`DELETE FROM ${escapeColumnName(tableName)}`);
    console.log(`🗑️  Cleared existing data from ${tableName}`);
  } catch (error) {
    console.error(`❌ Error clearing table ${tableName}:`, error.message);
    throw error;
  }
}

/**
 * Import data from a single JSON file
 */
async function importTableData(tableName, clearExisting = false) {
  const jsonPath = path.join(EXPORTS_DIR, `${tableName}.json`);
  
  if (!fs.existsSync(jsonPath)) {
    console.log(`⚠️  JSON file not found: ${jsonPath}`);
    return;
  }
  
  try {
    console.log(`📖 Reading data from ${jsonPath}...`);
    const jsonData = fs.readFileSync(jsonPath, 'utf-8');
    const data = JSON.parse(jsonData);
    
    if (!Array.isArray(data)) {
      console.error(`❌ Invalid data format in ${jsonPath}. Expected array.`);
      return;
    }
    
    if (data.length === 0) {
      console.log(`⚠️  No data found in ${jsonPath}`);
      return;
    }
    
    // Get column names from the first record
    const columns = Object.keys(data[0]);
    console.log(`📋 Columns for ${tableName}:`, columns);
    
    // Clear existing data if requested
    if (clearExisting) {
      await clearTable(tableName, true);
    }
    
    // Insert data in batches
    await insertDataInBatches(tableName, data, columns);
    
  } catch (error) {
    console.error(`❌ Error importing ${tableName}:`, error.message);
    throw error;
  }
}

/**
 * Get list of available JSON files
 */
function getAvailableJsonFiles() {
  if (!fs.existsSync(EXPORTS_DIR)) {
    console.error(`❌ Exports directory not found: ${EXPORTS_DIR}`);
    return [];
  }
  
  return fs.readdirSync(EXPORTS_DIR)
    .filter(file => file.endsWith('.json'))
    .map(file => path.basename(file, '.json'));
}

/**
 * Main import function
 */
async function importAllTables(clearExisting = false) {
  try {
    await client.connect();
    console.log('✅ Connected to remote PostgreSQL server');
    
    const availableTables = getAvailableJsonFiles();
    console.log(`📋 Found ${availableTables.length} JSON files to import:`, availableTables);
    
    if (availableTables.length === 0) {
      console.log('⚠️  No JSON files found to import');
      return;
    }
    
    // Import each table
    for (const tableName of availableTables) {
      console.log(`\n🔄 Processing table: ${tableName}`);
      await importTableData(tableName, clearExisting);
    }
    
    console.log('\n🎉 All tables imported successfully!');
    
  } catch (error) {
    console.error('❌ Error during import process:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('🔌 Connection closed');
  }
}

// Command line argument parsing
const args = process.argv.slice(2);
const clearExisting = args.includes('--clear') || args.includes('-c');
const specificTable = args.find(arg => !arg.startsWith('-'));

// Main execution
if (specificTable) {
  // Import specific table
  console.log(`🎯 Importing specific table: ${specificTable}`);
  client.connect()
    .then(() => importTableData(specificTable, clearExisting))
    .then(() => {
      console.log(`✅ Successfully imported ${specificTable}`);
      return client.end();
    })
    .catch(error => {
      console.error(`❌ Error importing ${specificTable}:`, error);
      client.end();
      process.exit(1);
    });
} else {
  // Import all tables
  console.log('🚀 Starting import process for all tables...');
  if (clearExisting) {
    console.log('⚠️  WARNING: Existing data will be cleared from all tables!');
  }
  importAllTables(clearExisting);
}
