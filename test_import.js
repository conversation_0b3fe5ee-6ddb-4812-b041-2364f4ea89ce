// test_import.js - Test script for database import functionality
import { Client } from 'pg';
import fs from 'fs';
import path from 'path';

// Database connection config (same as main scripts)
const client = new Client({
  host: 'localhost',
  port: 5433,
  user: 'postgres',
  password: 'SuperPassword123!',
  database: 'careerireland',
  ssl: { rejectUnauthorized: false }
});

/**
 * Test database connection
 */
async function testConnection() {
  try {
    await client.connect();
    console.log('✅ Database connection successful');
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time');
    console.log('✅ Database query successful:', result.rows[0].current_time);
    
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Test JSON file reading
 */
function testJsonFiles() {
  const exportsDir = path.join(process.cwd(), 'db_exports');
  
  if (!fs.existsSync(exportsDir)) {
    console.error('❌ db_exports directory not found');
    return false;
  }
  
  const jsonFiles = fs.readdirSync(exportsDir).filter(file => file.endsWith('.json'));
  console.log(`✅ Found ${jsonFiles.length} JSON files in db_exports/`);
  
  // Test reading a sample file
  if (jsonFiles.length > 0) {
    const sampleFile = jsonFiles[0];
    const samplePath = path.join(exportsDir, sampleFile);
    
    try {
      const content = fs.readFileSync(samplePath, 'utf-8');
      const data = JSON.parse(content);
      
      if (Array.isArray(data)) {
        console.log(`✅ Sample file ${sampleFile} is valid JSON array with ${data.length} records`);
        if (data.length > 0) {
          console.log('✅ Sample record structure:', Object.keys(data[0]));
        }
        return true;
      } else {
        console.error(`❌ Sample file ${sampleFile} is not a JSON array`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Error reading sample file ${sampleFile}:`, error.message);
      return false;
    }
  }
  
  return true;
}

/**
 * Test table existence
 */
async function testTableExistence() {
  try {
    const exportsDir = path.join(process.cwd(), 'db_exports');
    const jsonFiles = fs.readdirSync(exportsDir)
      .filter(file => file.endsWith('.json'))
      .map(file => path.basename(file, '.json'));
    
    console.log(`🔍 Checking if tables exist for ${jsonFiles.length} JSON files...`);
    
    for (const tableName of jsonFiles.slice(0, 5)) { // Test first 5 tables
      const result = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        );
      `, [tableName]);
      
      const exists = result.rows[0].exists;
      if (exists) {
        console.log(`✅ Table '${tableName}' exists`);
      } else {
        console.log(`⚠️  Table '${tableName}' does not exist`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error checking table existence:', error.message);
    return false;
  }
}

/**
 * Test data type handling
 */
function testDataTypeHandling() {
  console.log('🧪 Testing data type handling...');
  
  // Test formatValue function (copied from main script)
  function formatValue(value) {
    if (value === null || value === undefined) {
      return null;
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'number') {
      return value;
    }
    
    return value;
  }
  
  const testCases = [
    { input: null, expected: null, description: 'null value' },
    { input: undefined, expected: null, description: 'undefined value' },
    { input: 'string', expected: 'string', description: 'string value' },
    { input: 123, expected: 123, description: 'number value' },
    { input: true, expected: true, description: 'boolean value' },
    { input: { key: 'value' }, expected: '{"key":"value"}', description: 'object value' },
    { input: [1, 2, 3], expected: '[1,2,3]', description: 'array value' }
  ];
  
  let passed = 0;
  for (const testCase of testCases) {
    const result = formatValue(testCase.input);
    if (JSON.stringify(result) === JSON.stringify(testCase.expected)) {
      console.log(`✅ ${testCase.description}: passed`);
      passed++;
    } else {
      console.log(`❌ ${testCase.description}: failed (expected ${testCase.expected}, got ${result})`);
    }
  }
  
  console.log(`✅ Data type handling: ${passed}/${testCases.length} tests passed`);
  return passed === testCases.length;
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🧪 Starting import script tests...\n');
  
  const tests = [
    { name: 'JSON Files', test: testJsonFiles },
    { name: 'Data Type Handling', test: testDataTypeHandling },
    { name: 'Database Connection', test: testConnection },
    { name: 'Table Existence', test: testTableExistence }
  ];
  
  let passedTests = 0;
  
  for (const { name, test } of tests) {
    console.log(`\n🔍 Testing: ${name}`);
    console.log('─'.repeat(50));
    
    try {
      const result = await test();
      if (result) {
        console.log(`✅ ${name}: PASSED`);
        passedTests++;
      } else {
        console.log(`❌ ${name}: FAILED`);
      }
    } catch (error) {
      console.error(`❌ ${name}: ERROR -`, error.message);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`🏁 Test Results: ${passedTests}/${tests.length} tests passed`);
  
  if (passedTests === tests.length) {
    console.log('🎉 All tests passed! The import script should work correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the issues before running the import.');
  }
}

// Run tests and cleanup
runTests()
  .finally(() => {
    client.end().catch(() => {}); // Ignore connection close errors
  });
